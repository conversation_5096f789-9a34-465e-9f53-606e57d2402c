'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';

export default function AuthCallbackPage() {
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const token = searchParams.get('token');
    const error = searchParams.get('error');
    const redirectPath = searchParams.get('redirect');

    if (error) {
      setStatus('error');
      setMessage('Authentication failed. Please try again.');
      setTimeout(() => {
        window.location.href = '/auth/login';
      }, 3000);
      return;
    }

    if (token) {
      // Store the token and redirect based on user role
      localStorage.setItem('auth_token', token);
      setStatus('success');
      setMessage('Authentication successful! Redirecting...');

      // Fetch user profile to determine role-based redirect
      const fetchUserAndRedirect = async () => {
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/profile`, {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          });

          if (response.ok) {
            const userData = await response.json();
            let targetPath = '/';

            // Use redirect parameter if provided, otherwise determine based on role
            if (redirectPath) {
              targetPath = decodeURIComponent(redirectPath);
            } else if (userData.role === 'admin') {
              targetPath = '/admin';
            } else {
              targetPath = '/';
            }

            setTimeout(() => {
              window.location.href = targetPath;
            }, 2000);
          } else {
            throw new Error('Failed to fetch user profile');
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
          // Fallback to home page
          setTimeout(() => {
            window.location.href = '/';
          }, 2000);
        }
      };

      fetchUserAndRedirect();
    } else {
      setStatus('error');
      setMessage('No authentication token received.');
      setTimeout(() => {
        window.location.href = '/auth/login';
      }, 3000);
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-muted/50 to-background px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center"
      >
        <div className="mb-6">
          {status === 'loading' && (
            <div className="w-16 h-16 mx-auto mb-4 border-4 border-accent border-t-transparent rounded-full animate-spin" />
          )}
          {status === 'success' && (
            <CheckCircleIcon className="w-16 h-16 mx-auto mb-4 text-success" />
          )}
          {status === 'error' && (
            <XCircleIcon className="w-16 h-16 mx-auto mb-4 text-destructive" />
          )}
        </div>

        <h1 className="text-2xl font-bold text-foreground mb-4">
          {status === 'loading' && 'Authenticating...'}
          {status === 'success' && 'Welcome to Bedazzled!'}
          {status === 'error' && 'Authentication Failed'}
        </h1>

        <p className="text-muted-foreground">
          {message}
        </p>

        {status === 'loading' && (
          <div className="mt-8">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-accent rounded-full animate-bounce" />
              <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
              <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
}

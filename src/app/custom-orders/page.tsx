'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { SparklesIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/auth-context';
import { ImageUpload } from '@/components/custom-orders/image-upload';
import Link from 'next/link';

export default function CustomOrdersPage() {
  const { isAuthenticated } = useAuth();

  const [formData, setFormData] = useState({
    customerName: '',
    email: '',
    phone: '',
    description: '',
    size: 'medium',
    bedazzlingLevel: 'medium',
    frameOption: 'premium',
    quantity: 1,
    budget: 15000,
    referenceImages: [] as File[]
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImagesChange = (images: File[]) => {
    setFormData(prev => ({
      ...prev,
      referenceImages: images
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isAuthenticated) {
      window.location.href = '/auth/login?redirect=/custom-orders';
      return;
    }

    setIsSubmitting(true);

    try {
      // Create FormData for file upload
      const formDataToSend = new FormData();

      // Add form fields
      formDataToSend.append('customerName', formData.customerName);
      formDataToSend.append('email', formData.email);
      formDataToSend.append('phone', formData.phone);
      formDataToSend.append('description', formData.description);
      formDataToSend.append('size', formData.size);
      formDataToSend.append('bedazzlingLevel', formData.bedazzlingLevel);
      formDataToSend.append('frameOption', formData.frameOption);
      formDataToSend.append('quantity', formData.quantity.toString());
      formDataToSend.append('budget', formData.budget.toString());

      // Add reference images
      formData.referenceImages.forEach((file) => {
        formDataToSend.append('referenceImages', file);
      });

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/custom-orders`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: formDataToSend,
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Custom order request submitted successfully! Order ID: ${result.id || 'N/A'}`);

        // Reset form
        setFormData({
          customerName: '',
          email: '',
          phone: '',
          description: '',
          size: 'medium',
          bedazzlingLevel: 'medium',
          frameOption: 'premium',
          quantity: 1,
          budget: 15000,
          referenceImages: []
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to submit order');
      }
    } catch (error) {
      console.error('Error submitting order:', error);
      alert(`Failed to submit order: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isAuthenticated) {
    return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-12"
      >
        <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-6">
          Custom Bedazzled Portraits
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
          Transform your precious memories into stunning, one-of-a-kind rhinestone artwork.
          Each piece is meticulously handcrafted by our Kenyan artisans with premium materials.
        </p>
        <div className="flex flex-wrap justify-center gap-4 mb-8">
          <Badge variant="secondary" className="text-sm px-4 py-2">
            ✨ Premium Rhinestones
          </Badge>
          <Badge variant="secondary" className="text-sm px-4 py-2">
            🎨 Custom Design
          </Badge>
          <Badge variant="secondary" className="text-sm px-4 py-2">
            🚚 Free Delivery in Nairobi
          </Badge>
          <Badge variant="secondary" className="text-sm px-4 py-2">
            💎 Lifetime Guarantee
          </Badge>
        </div>

        {/* Sign In CTA */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 rounded-2xl p-8 mb-12"
        >
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
              <UserPlusIcon className="h-8 w-8 text-white" />
            </div>
          </div>
          <h2 className="text-2xl font-bold text-foreground mb-4">
            Sign In to Place Your Custom Order
          </h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Create an account to access our full custom order system, track your orders,
            save preferences, and enjoy exclusive member benefits.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/login?redirect=/custom-orders">
              <Button size="lg" className="w-full sm:w-auto">
                <LockClosedIcon className="h-5 w-5 mr-2" />
                Sign In to Order
              </Button>
            </Link>
            <Link href="/auth/register?redirect=/custom-orders">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                Create Account
              </Button>
            </Link>
          </div>
        </motion.div>
      </motion.div>



      {/* Pricing Tiers Preview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
        className="mb-12"
      >
        <h2 className="text-3xl font-bold text-foreground text-center mb-8">
          Pricing Tiers
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[
            { size: 'Small', price: 8000, description: '8" x 10" portrait' },
            { size: 'Medium', price: 12000, description: '11" x 14" portrait' },
            { size: 'Large', price: 18000, description: '16" x 20" portrait' },
            { size: 'Extra Large', price: 25000, description: '20" x 24" portrait' }
          ].map((tier, index) => (
            <motion.div
              key={tier.size}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
            >
              <Card className="text-center p-6 hover:shadow-lg transition-shadow">
                <h3 className="text-xl font-bold text-foreground mb-2">{tier.size}</h3>
                <div className="text-3xl font-bold text-accent mb-2">
                  {formatPrice(tier.price)}
                </div>
                <p className="text-sm text-muted-foreground mb-4">{tier.description}</p>
                <p className="text-xs text-muted-foreground">Starting price</p>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Benefits of Creating Account */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.7 }}
        className="mb-12"
      >
        <Card className="bg-gradient-to-br from-primary/5 to-accent/5 border-primary/20">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-foreground mb-4">
              Why Create an Account?
            </CardTitle>
            <CardDescription className="text-base">
              Unlock exclusive benefits and streamlined ordering experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  icon: TruckIcon,
                  title: 'Order Tracking',
                  description: 'Real-time updates on your custom portrait progress'
                },
                {
                  icon: HeartIcon,
                  title: 'Saved Preferences',
                  description: 'Save your favorite styles and specifications'
                },
                {
                  icon: StarIcon,
                  title: 'Quick Reordering',
                  description: 'Easily reorder with saved templates'
                },
                {
                  icon: ShieldCheckIcon,
                  title: 'Priority Support',
                  description: 'Direct access to our customer support team'
                }
              ].map((benefit, index) => (
                <motion.div
                  key={benefit.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <benefit.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-foreground mb-2">{benefit.title}</h3>
                  <p className="text-sm text-muted-foreground">{benefit.description}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Preview Form (Disabled) */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.9 }}
      >
        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-muted/50 z-10 flex items-center justify-center">
            <div className="text-center bg-background/95 backdrop-blur-sm border border-border rounded-lg p-8 max-w-md">
              <LockClosedIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">
                Sign In Required
              </h3>
              <p className="text-muted-foreground mb-6">
                Create an account to access our interactive custom order form
              </p>
              <div className="flex flex-col gap-3">
                <Link href="/auth/login?redirect=/custom-orders">
                  <Button className="w-full">
                    Sign In to Continue
                  </Button>
                </Link>
                <Link href="/auth/register?redirect=/custom-orders">
                  <Button variant="outline" className="w-full">
                    Create Account
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Preview Form Content (Blurred) */}
          <CardHeader>
            <CardTitle className="flex items-center gap-2 opacity-50">
              <SparklesIcon className="h-6 w-6 text-accent" />
              Custom Order Details
            </CardTitle>
            <CardDescription className="opacity-50">
              Configure your custom bedazzled portrait and see real-time pricing
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 opacity-30">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground">Customer Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Full Name *
                  </label>
                  <Input
                    type="text"
                    placeholder="Enter your full name"
                    disabled
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Email Address *
                  </label>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    disabled
                  />
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground">Product Specifications</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {['Small', 'Medium', 'Large', 'Extra Large'].map(size => (
                  <Button key={size} variant="outline" disabled className="capitalize">
                    {size}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );

  // Authenticated User Component
  const AuthenticatedOrderForm = () => (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-12"
      >
        <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-6">
          Custom Bedazzled Portraits
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
          Transform your precious memories into stunning, one-of-a-kind rhinestone artwork.
          Each piece is meticulously handcrafted by our Kenyan artisans with premium materials.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Badge variant="secondary" className="text-sm px-4 py-2">
            ✨ Premium Rhinestones
          </Badge>
          <Badge variant="secondary" className="text-sm px-4 py-2">
            🎨 Custom Design
          </Badge>
          <Badge variant="secondary" className="text-sm px-4 py-2">
            🚚 Free Delivery in Nairobi
          </Badge>
          <Badge variant="secondary" className="text-sm px-4 py-2">
            💎 Lifetime Guarantee
          </Badge>
        </div>
      </motion.div>

      {/* Main Form Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Form */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SparklesIcon className="h-6 w-6 text-accent" />
                  Custom Order Details
                </CardTitle>
                <CardDescription>
                  Configure your custom bedazzled portrait and see real-time pricing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Customer Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">Customer Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Full Name *
                        </label>
                        <Input
                          type="text"
                          value={formData.customerName}
                          onChange={(e) => handleInputChange('customerName', e.target.value)}
                          placeholder="Enter your full name"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Email Address *
                        </label>
                        <Input
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Phone Number *
                        </label>
                        <Input
                          type="tel"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          placeholder="+254 700 000 000"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  {/* Product Specifications */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">Product Specifications</h3>

                    {/* Size Selection */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Size *
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {(['small', 'medium', 'large', 'extra-large'] as const).map(size => (
                          <Button
                            key={size}
                            type="button"
                            variant={specs.size === size ? 'default' : 'outline'}
                            onClick={() => handleSpecChange('size', size)}
                            className="capitalize"
                          >
                            {size.replace('-', ' ')}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Bedazzling Level */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Bedazzling Level *
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {(['light', 'medium', 'heavy', 'premium'] as const).map(level => (
                          <Button
                            key={level}
                            type="button"
                            variant={specs.bedazzlingLevel === level ? 'default' : 'outline'}
                            onClick={() => handleSpecChange('bedazzlingLevel', level)}
                            className="capitalize"
                          >
                            {level}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Frame Option */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Frame Option *
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {(['basic', 'premium', 'luxury'] as const).map(frame => (
                          <Button
                            key={frame}
                            type="button"
                            variant={specs.frameOption === frame ? 'default' : 'outline'}
                            onClick={() => handleSpecChange('frameOption', frame)}
                            className="capitalize"
                          >
                            {frame}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Complexity */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Design Complexity *
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {(['simple', 'detailed', 'intricate'] as const).map(complexity => (
                          <Button
                            key={complexity}
                            type="button"
                            variant={specs.complexity === complexity ? 'default' : 'outline'}
                            onClick={() => handleSpecChange('complexity', complexity)}
                            className="capitalize"
                          >
                            {complexity}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Quantity */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Quantity
                      </label>
                      <div className="flex items-center gap-3">
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => adjustQuantity(-1)}
                          disabled={specs.quantity <= 1}
                        >
                          <MinusIcon className="h-4 w-4" />
                        </Button>
                        <span className="text-lg font-semibold w-12 text-center">
                          {specs.quantity}
                        </span>
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => adjustQuantity(1)}
                        >
                          <PlusIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Rush Order */}
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="rushOrder"
                        checked={specs.rushOrder}
                        onChange={(e) => handleSpecChange('rushOrder', e.target.checked)}
                        className="rounded border-border"
                      />
                      <label htmlFor="rushOrder" className="text-sm font-medium text-foreground">
                        Rush Order (+50% fee, faster delivery)
                      </label>
                    </div>
                  </div>

                  {/* Additional Features */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">Additional Features</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {ADDITIONAL_FEATURES.map(feature => (
                        <div key={feature.id} className="flex items-start space-x-3">
                          <input
                            type="checkbox"
                            id={feature.id}
                            checked={specs.additionalFeatures.includes(feature.id)}
                            onChange={() => toggleFeature(feature.id)}
                            className="mt-1 rounded border-border"
                          />
                          <div className="flex-1">
                            <label htmlFor={feature.id} className="text-sm font-medium text-foreground cursor-pointer">
                              {feature.name} (+{formatPrice(feature.price)})
                            </label>
                            <p className="text-xs text-muted-foreground">{feature.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Project Description */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Project Description *
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Describe your vision, the subject of the portrait, any specific requirements..."
                      className="w-full min-h-[120px] px-3 py-2 border border-border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-accent"
                      required
                    />
                  </div>

                  {/* Image Upload */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Reference Images
                    </label>
                    <ImageUpload
                      images={formData.referenceImages}
                      onImagesChange={handleImagesChange}
                      maxImages={10}
                      maxSizePerImage={5}
                    />
                  </div>

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    size="lg"
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      'Submitting...'
                    ) : (
                      <>
                        Submit Custom Order Request
                        <ArrowRightIcon className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Price Estimation Sidebar */}
        <div className="lg:col-span-1">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="sticky top-8"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalculatorIcon className="h-5 w-5 text-accent" />
                  Price Estimation
                </CardTitle>
                <CardDescription>
                  Real-time pricing based on your selections
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Price Breakdown */}
                <div className="space-y-2">
                  {estimation.breakdown.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-muted-foreground">{item.label}</span>
                      <span className={item.amount < 0 ? 'text-green-600' : 'text-foreground'}>
                        {item.amount < 0 ? '-' : ''}{formatPrice(Math.abs(item.amount))}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="border-t border-border pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-foreground">Total</span>
                    <span className="text-2xl font-bold text-accent">
                      {formatPrice(estimation.total)}
                    </span>
                  </div>
                </div>

                {/* Delivery Time */}
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <ClockIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-foreground">Estimated Delivery</span>
                  </div>
                  <p className="text-sm text-muted-foreground">{deliveryTime.description}</p>
                </div>

                {/* Features Summary */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-foreground">Selected Features:</h4>
                  <div className="space-y-1">
                    <Badge variant="secondary" className="text-xs">
                      {specs.size.replace('-', ' ')} size
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {specs.bedazzlingLevel} bedazzling
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {specs.frameOption} frame
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {specs.complexity} complexity
                    </Badge>
                    {specs.quantity > 1 && (
                      <Badge variant="secondary" className="text-xs">
                        Qty: {specs.quantity}
                      </Badge>
                    )}
                    {specs.rushOrder && (
                      <Badge variant="destructive" className="text-xs">
                        Rush Order
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Info Note */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <InformationCircleIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-blue-800 font-medium">Price Estimate</p>
                      <p className="text-xs text-blue-700">
                        This is an estimated price. Final quote will be provided after reviewing your requirements.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );

  // Main component return - conditionally render based on authentication
  if (!isAuthenticated) {
    return <GuestPreview />;
  }

  return <AuthenticatedOrderForm />;
}

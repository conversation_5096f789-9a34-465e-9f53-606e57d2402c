'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ShoppingBagIcon,
  ArrowRightIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CartItem } from '@/components/cart/cart-item';
import { formatPrice } from '@/lib/utils';
import { Cart, CartItem as CartItemType } from '@/types';

// Mock cart data
const mockCart: Cart = {
  id: 'cart-1',
  userId: 'user-1',
  items: [
    {
      id: 'item-1',
      productId: 'product-1',
      variantId: 'variant-1',
      quantity: 2,
      product: {
        id: 'product-1',
        name: 'Elegant Portrait - Classic Style',
        description: 'A beautiful bedazzled portrait with classic rhinestone arrangement',
        category: 'pre-made',
        images: ['/products/portrait1.jpg'],
        variants: [],
        basePrice: 299.99,
        featured: true,
        tags: ['elegant', 'classic'],
        rating: 4.8,
        reviewCount: 24,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      variant: {
        id: 'variant-1',
        size: 'medium',
        bedazzlingLevel: 'medium',
        frameOption: 'premium',
        price: 299.99,
        stock: 5
      }
    },
    {
      id: 'item-2',
      productId: 'product-2',
      variantId: 'variant-2',
      quantity: 1,
      product: {
        id: 'product-2',
        name: 'Glamorous Bedazzled Art',
        description: 'Stunning rhinestone artwork with premium crystals',
        category: 'pre-made',
        images: ['/products/portrait2.jpg'],
        variants: [],
        basePrice: 499.99,
        featured: false,
        tags: ['glamorous', 'premium'],
        rating: 4.9,
        reviewCount: 18,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      variant: {
        id: 'variant-2',
        size: 'large',
        bedazzlingLevel: 'heavy',
        frameOption: 'luxury',
        price: 499.99,
        stock: 3
      }
    }
  ],
  total: 1099.97,
  createdAt: new Date(),
  updatedAt: new Date()
};

export default function CartPage() {
  const [cart, setCart] = useState<Cart | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch cart on component mount
  useEffect(() => {
    fetchCart();
  }, []);

  const fetchCart = async () => {
    try {
      setIsLoading(true);
      // TODO: Replace with actual cart API endpoint
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/cart`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      if (response.ok) {
        const cartData = await response.json();
        setCart(cartData);
      } else {
        // Initialize empty cart if none exists
        setCart({
          id: '',
          userId: '',
          items: [],
          total: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
    } catch (error) {
      console.error('Error fetching cart:', error);
      // Initialize empty cart on error
      setCart({
        id: '',
        userId: '',
        items: [],
        total: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateQuantity = (itemId: string, quantity: number) => {
    if (!cart) return;

    setCart(prevCart => {
      if (!prevCart) return null;
      const updatedItems = prevCart.items.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      );
      return {
        ...prevCart,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    });
  };

  const handleRemoveItem = (itemId: string) => {
    if (!cart) return;

    setCart(prevCart => {
      if (!prevCart) return null;
      const updatedItems = prevCart.items.filter(item => item.id !== itemId);
      return {
        ...prevCart,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    });
  };

  const calculateTotal = (items: CartItemType[]) => {
    return items.reduce((total, item) => total + (item.variant.price * item.quantity), 0);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your cart...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (!cart) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <p className="text-muted-foreground">Unable to load cart. Please try again.</p>
        </div>
      </div>
    );
  }

  const subtotal = calculateTotal(cart.items);
  const shipping = subtotal > 200 ? 0 : 15.99;
  const tax = subtotal * 0.08; // 8% tax
  const total = subtotal + shipping + tax;

  if (cart.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-accent/20 to-highlight/20 rounded-full flex items-center justify-center">
              <ShoppingBagIcon className="h-12 w-12 text-muted-foreground" />
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-4">
              Your Cart is Empty
            </h1>
            <p className="text-muted-foreground mb-8">
              Looks like you haven't added any bedazzled portraits to your cart yet.
            </p>
            <Link href="/products">
              <Button size="lg">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Continue Shopping
              </Button>
            </Link>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="mb-8"
      >
        <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
          Shopping Cart
        </h1>
        <p className="text-muted-foreground">
          Review your selected items and proceed to checkout
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Cart Items ({cart.items.length})</span>
                <Link href="/products">
                  <Button variant="outline" size="sm">
                    <ArrowLeftIcon className="h-4 w-4 mr-2" />
                    Continue Shopping
                  </Button>
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <AnimatePresence>
                {cart.items.map((item) => (
                  <CartItem
                    key={item.id}
                    item={item}
                    onUpdateQuantity={handleUpdateQuantity}
                    onRemove={handleRemoveItem}
                  />
                ))}
              </AnimatePresence>
            </CardContent>
          </Card>
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <Card className="sticky top-4">
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Summary Details */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>{formatPrice(subtotal)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Shipping</span>
                  <span>
                    {shipping === 0 ? (
                      <Badge variant="success" className="text-xs">Free</Badge>
                    ) : (
                      formatPrice(shipping)
                    )}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Tax</span>
                  <span>{formatPrice(tax)}</span>
                </div>
                <div className="border-t border-border pt-2">
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>{formatPrice(total)}</span>
                  </div>
                </div>
              </div>

              {/* Free Shipping Notice */}
              {shipping > 0 && (
                <div className="p-3 bg-muted rounded-md">
                  <p className="text-xs text-muted-foreground">
                    Add {formatPrice(200 - subtotal)} more to get free shipping!
                  </p>
                </div>
              )}

              {/* Checkout Button */}
              <Button 
                size="lg" 
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  'Processing...'
                ) : (
                  <>
                    Proceed to Checkout
                    <ArrowRightIcon className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>

              {/* Security Notice */}
              <div className="text-center">
                <p className="text-xs text-muted-foreground">
                  🔒 Secure checkout with SSL encryption
                </p>
              </div>

              {/* Payment Methods */}
              <div className="text-center">
                <p className="text-xs text-muted-foreground mb-2">We accept:</p>
                <div className="flex justify-center gap-2">
                  <div className="w-8 h-5 bg-muted rounded flex items-center justify-center text-xs">💳</div>
                  <div className="w-8 h-5 bg-muted rounded flex items-center justify-center text-xs">🏦</div>
                  <div className="w-8 h-5 bg-muted rounded flex items-center justify-center text-xs">📱</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

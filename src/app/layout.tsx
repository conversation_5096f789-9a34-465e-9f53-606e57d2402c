import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/header";
import { AuthProvider } from "@/contexts/auth-context";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: {
    default: "Dazzled - Premium Rhinestone Bedazzled Portraits",
    template: "%s | Dazzled"
  },
  description: "Transform your precious memories into stunning, one-of-a-kind rhinestone artwork. Each piece is meticulously handcrafted by our Kenyan artisans with premium materials.",
  keywords: ["dazzled", "bedazzled", "rhinestone", "portraits", "custom art", "personalized gifts", "kenya", "handcrafted", "premium"],
  authors: [{ name: "Dazzled Team" }],
  creator: "Dazzled",
  publisher: "Dazzled",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://dazzled.co.ke'),
  alternates: {
    canonical: '/',
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/icon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/icon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/icon-48x48.png', sizes: '48x48', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#8b5cf6' },
    ],
  },
  manifest: '/site.webmanifest',
  openGraph: {
    type: "website",
    locale: "en_KE",
    url: "https://dazzled.co.ke",
    title: "Dazzled - Premium Rhinestone Bedazzled Portraits",
    description: "Transform your precious memories into stunning, one-of-a-kind rhinestone artwork. Handcrafted by Kenyan artisans.",
    siteName: "Dazzled",
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Dazzled - Premium Rhinestone Bedazzled Portraits',
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Dazzled - Premium Rhinestone Bedazzled Portraits",
    description: "Transform your precious memories into stunning, one-of-a-kind rhinestone artwork. Handcrafted by Kenyan artisans.",
    creator: "@dazzled_ke",
    images: ['/twitter-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="min-h-screen bg-background font-sans antialiased">
        <AuthProvider>
          <div className="relative flex min-h-screen flex-col">
            <Header />
            <main className="flex-1">{children}</main>
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}

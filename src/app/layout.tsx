import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Head<PERSON> } from "@/components/layout/header";
import { AuthProvider } from "@/contexts/auth-context";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "Bedazzled - Rhinestone Bedazzled Portraits",
  description: "Transform your memories into stunning rhinestone bedazzled portraits. Custom and pre-made options available.",
  keywords: ["bedazzled", "rhinestone", "portraits", "custom art", "personalized gifts"],
  authors: [{ name: "Bedazzled Team" }],
  creator: "Bedazzled",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://bedazzled.com",
    title: "Bedazzled - Rhinestone Bedazzled Portraits",
    description: "Transform your memories into stunning rhinestone bedazzled portraits.",
    siteName: "Bedazzled",
  },
  twitter: {
    card: "summary_large_image",
    title: "Bedazzled - Rhinestone Bedazzled Portraits",
    description: "Transform your memories into stunning rhinestone bedazzled portraits.",
    creator: "@bedazzled",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="min-h-screen bg-background font-sans antialiased">
        <AuthProvider>
          <div className="relative flex min-h-screen flex-col">
            <Header />
            <main className="flex-1">{children}</main>
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  SparklesIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ProductGrid } from '@/components/products/product-grid';
import { ProductFilters } from '@/components/products/product-filters';
import { useAuth } from '@/contexts/auth-context';
import { Product, ProductFilters as FilterType } from '@/types';
import { KENYAN_CONTENT, getTimeBasedGreeting } from '@/data/localization';



export default function Home() {
  const { isAuthenticated } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [sortBy, setSortBy] = useState('newest');
  const [isLoading, setIsLoading] = useState(true);

  const [filters, setFilters] = useState<FilterType>({
    category: undefined,
    size: [],
    bedazzlingLevel: [],
    frameOption: [],
    priceRange: undefined,
    rating: undefined,
    inStock: undefined,
    featured: undefined,
  });

  // Fetch products from API
  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/products?featured=true&limit=20`);

      if (response.ok) {
        const data = await response.json();
        const fetchedProducts = data.data || [];
        setProducts(fetchedProducts);
        setFilteredProducts(fetchedProducts);
      } else {
        console.error('Failed to fetch products');
        setProducts([]);
        setFilteredProducts([]);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      setProducts([]);
      setFilteredProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filters and search
  useEffect(() => {
    let filtered = [...products];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Category filter
    if (filters.category) {
      filtered = filtered.filter(product => product.category === filters.category);
    }

    // Size filter
    if (filters.size && filters.size.length > 0) {
      filtered = filtered.filter(product =>
        product.variants.some(variant => filters.size!.includes(variant.size))
      );
    }

    // Bedazzling level filter
    if (filters.bedazzlingLevel && filters.bedazzlingLevel.length > 0) {
      filtered = filtered.filter(product =>
        product.variants.some(variant => filters.bedazzlingLevel!.includes(variant.bedazzlingLevel))
      );
    }

    // Frame option filter
    if (filters.frameOption && filters.frameOption.length > 0) {
      filtered = filtered.filter(product =>
        product.variants.some(variant => filters.frameOption!.includes(variant.frameOption))
      );
    }

    // Price range filter
    if (filters.priceRange) {
      filtered = filtered.filter(product =>
        product.basePrice >= filters.priceRange![0] &&
        product.basePrice <= filters.priceRange![1]
      );
    }

    // Rating filter
    if (filters.rating) {
      filtered = filtered.filter(product => product.rating >= filters.rating!);
    }

    // In stock filter
    if (filters.inStock) {
      filtered = filtered.filter(product =>
        product.variants.some(variant => variant.stock > 0)
      );
    }

    // Featured filter
    if (filters.featured) {
      filtered = filtered.filter(product => product.featured);
    }

    // Sort products
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.basePrice - b.basePrice);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.basePrice - a.basePrice);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'popular':
        filtered.sort((a, b) => b.reviewCount - a.reviewCount);
        break;
      case 'newest':
      default:
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
    }

    setFilteredProducts(filtered);
  }, [products, searchQuery, filters, sortBy]);

  const handleFiltersChange = (newFilters: FilterType) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({
      category: undefined,
      size: [],
      bedazzlingLevel: [],
      frameOption: [],
      priceRange: undefined,
      rating: undefined,
      inStock: undefined,
      featured: undefined,
    });
    setSearchQuery('');
  };

  const handleAddToWishlist = (productId: string) => {
    if (!isAuthenticated) {
      window.location.href = '/auth/login';
      return;
    }
    console.log('Add to wishlist:', productId);
  };

  const handleAddToCart = (productId: string, variantId: string) => {
    if (!isAuthenticated) {
      window.location.href = '/auth/login';
      return;
    }
    console.log('Add to cart:', productId, variantId);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Marketplace Header */}
      <section className="bg-gradient-to-br from-background via-muted/30 to-background border-b border-border">
        <div className="container mx-auto px-4 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-6"
          >
            <div className="space-y-4">
              <Badge variant="secondary" className="w-fit mx-auto">
                ✨ {KENYAN_CONTENT.welcome.subtitle}
              </Badge>
              <h1 className="text-3xl lg:text-5xl font-bold text-foreground leading-tight">
                {getTimeBasedGreeting()}! Welcome to
                <span className="bg-gradient-to-r from-primary via-accent to-highlight bg-clip-text text-transparent">
                  {' '}Bedazzled{' '}
                </span>
                Kenya
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                {KENYAN_CONTENT.welcome.description}
              </p>
            </div>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search for bedazzled portraits..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 pr-4 h-12 text-base"
                />
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  if (!isAuthenticated) {
                    window.location.href = '/auth/login';
                  } else {
                    window.location.href = '/custom-orders';
                  }
                }}
              >
                Custom Order
              </Button>
              <Link href="/about">
                <Button variant="ghost" size="lg">
                  About Us
                </Button>
              </Link>
            </div>

            {/* Features */}
            <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
              {KENYAN_CONTENT.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <span className="text-lg">{feature.icon}</span>
                  <span>{feature.title}</span>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>


      {/* Marketplace Products Section */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          {/* Filters and Sort Controls */}
          <div className="flex flex-col gap-4 mb-6 lg:mb-8">
            {/* Mobile Controls Row */}
            <div className="flex flex-col sm:flex-row gap-4 sm:items-center sm:justify-between">
              <div className="flex flex-col sm:flex-row gap-4 sm:items-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFiltersOpen(!filtersOpen)}
                  className="lg:hidden w-full sm:w-auto"
                >
                  <FunnelIcon className="h-4 w-4 mr-2" />
                  Filters
                </Button>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground whitespace-nowrap">Sort by:</span>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="text-sm border border-border rounded-md px-3 py-1 bg-background flex-1 sm:flex-none"
                  >
                    <option value="newest">Newest First</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="rating">Highest Rated</option>
                    <option value="popular">Most Popular</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center justify-between sm:justify-end gap-4">
                <span className="text-sm text-muted-foreground">
                  {filteredProducts.length} products
                </span>
                <div className="flex border border-border rounded-md">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-8">
            {/* Filters Sidebar */}
            <div className="lg:col-span-1 order-2 lg:order-1">
              <ProductFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onClearFilters={handleClearFilters}
                isOpen={filtersOpen}
                onToggle={() => setFiltersOpen(!filtersOpen)}
              />
            </div>

            {/* Products Grid */}
            <div className="lg:col-span-3 order-1 lg:order-2">
              <ProductGrid
                products={filteredProducts}
                onAddToWishlist={handleAddToWishlist}
                onAddToCart={handleAddToCart}
              />

              {filteredProducts.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-accent/20 to-highlight/20 rounded-full flex items-center justify-center">
                    <SparklesIcon className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">No Products Found</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchQuery
                      ? `No products found matching "${searchQuery}". Try adjusting your search or filters.`
                      : 'Try adjusting your filters to find what you\'re looking for.'
                    }
                  </p>
                  <Button onClick={handleClearFilters} variant="outline">
                    Clear All Filters
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

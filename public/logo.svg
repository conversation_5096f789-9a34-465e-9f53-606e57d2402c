<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="sparkleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main background -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="8"/>
  
  <!-- Central diamond/rhinestone -->
  <path d="M256 120 L320 200 L256 280 L192 200 Z" fill="url(#sparkleGradient)" stroke="#ffffff" stroke-width="3"/>
  
  <!-- Sparkle effects around the diamond -->
  <g fill="#fbbf24" opacity="0.9">
    <!-- Top sparkles -->
    <path d="M256 80 L260 90 L270 86 L266 96 L276 100 L266 104 L270 114 L260 110 L256 120 L252 110 L242 114 L246 104 L236 100 L246 96 L242 86 L252 90 Z"/>
    
    <!-- Right sparkles -->
    <path d="M380 200 L384 210 L394 206 L390 216 L400 220 L390 224 L394 234 L384 230 L380 240 L376 230 L366 234 L370 224 L360 220 L370 216 L366 206 L376 210 Z"/>
    
    <!-- Bottom sparkles -->
    <path d="M256 360 L260 370 L270 366 L266 376 L276 380 L266 384 L270 394 L260 390 L256 400 L252 390 L242 394 L246 384 L236 380 L246 376 L242 366 L252 370 Z"/>
    
    <!-- Left sparkles -->
    <path d="M132 200 L136 210 L146 206 L142 216 L152 220 L142 224 L146 234 L136 230 L132 240 L128 230 L118 234 L122 224 L112 220 L122 216 L118 206 L128 210 Z"/>
  </g>
  
  <!-- Smaller accent sparkles -->
  <g fill="#ffffff" opacity="0.8">
    <circle cx="200" cy="150" r="4"/>
    <circle cx="312" cy="150" r="4"/>
    <circle cx="350" cy="256" r="4"/>
    <circle cx="162" cy="256" r="4"/>
    <circle cx="200" cy="362" r="4"/>
    <circle cx="312" cy="362" r="4"/>
  </g>
  
  <!-- Letter "D" in the center -->
  <text x="256" y="280" font-family="serif" font-size="120" font-weight="bold" fill="#ffffff" text-anchor="middle">D</text>
</svg>
